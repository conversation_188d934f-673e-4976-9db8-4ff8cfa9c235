import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#661F15', // Ancient red
      light: '#8B4513', // Lighter red-brown
      dark: '#4A1611', // Darker red
      contrastText: '#F5F5F0'
    },
    secondary: {
      main: '#B8860B', // Golden Palace copper
      light: '#FFD700', // Gold
      dark: '#8B6914', // Darker gold
      contrastText: '#1E1E1E'
    },
    background: {
      default: '#F5F5F0', // Rice white
      paper: '#FFFFFF'
    },
    text: {
      primary: '#1E1E1E', // Deep gray
      secondary: '#666666'
    },
    grey: {
      50: '#FAFAFA',
      100: '#F5F5F0',
      200: '#EEEEEE',
      300: '#E0E0E0',
      400: '#BDBDBD',
      500: '#9E9E9E',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#1E1E1E'
    },
    common: {
      black: '#1E1E1E',
      white: '#F5F5F0'
    }
  },
  typography: {
    fontFamily: '"Source Han Serif SC", "Noto Serif SC", serif',
    h1: {
      fontFamily: '"Ma Shan Zheng", "Source Han Serif SC", serif',
      fontSize: '3.5rem',
      fontWeight: 700,
      lineHeight: 1.2
    },
    h2: {
      fontFamily: '"Ma Shan Zheng", "Source Han Serif SC", serif',
      fontSize: '2.75rem',
      fontWeight: 600,
      lineHeight: 1.3
    },
    h3: {
      fontFamily: '"Ma Shan Zheng", "Source Han Serif SC", serif',
      fontSize: '2.25rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    h4: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    h5: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.5
    },
    h6: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.5
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.7
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6
    }
  },
  shape: {
    borderRadius: 12
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(30, 30, 30, 0.1)',
    '0px 4px 8px rgba(30, 30, 30, 0.12)',
    '0px 8px 16px rgba(30, 30, 30, 0.14)',
    '0px 12px 24px rgba(30, 30, 30, 0.16)',
    '0px 16px 32px rgba(30, 30, 30, 0.18)',
    '0px 20px 40px rgba(30, 30, 30, 0.20)',
    '0px 24px 48px rgba(30, 30, 30, 0.22)',
    '0px 32px 64px rgba(30, 30, 30, 0.24)',
    '0px 40px 80px rgba(30, 30, 30, 0.26)',
    '0px 48px 96px rgba(30, 30, 30, 0.28)',
    '0px 56px 112px rgba(30, 30, 30, 0.30)',
    '0px 64px 128px rgba(30, 30, 30, 0.32)',
    '0px 72px 144px rgba(30, 30, 30, 0.34)',
    '0px 80px 160px rgba(30, 30, 30, 0.36)',
    '0px 88px 176px rgba(30, 30, 30, 0.38)',
    '0px 96px 192px rgba(30, 30, 30, 0.40)',
    '0px 104px 208px rgba(30, 30, 30, 0.42)',
    '0px 112px 224px rgba(30, 30, 30, 0.44)',
    '0px 120px 240px rgba(30, 30, 30, 0.46)',
    '0px 128px 256px rgba(30, 30, 30, 0.48)',
    '0px 136px 272px rgba(30, 30, 30, 0.50)',
    '0px 144px 288px rgba(30, 30, 30, 0.52)',
    '0px 152px 304px rgba(30, 30, 30, 0.54)',
    '0px 160px 320px rgba(30, 30, 30, 0.56)'
  ]
});

export default theme;