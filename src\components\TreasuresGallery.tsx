import React, { useState } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, IconButton, Chip, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import View3DIcon from '@mui/icons-material/ThreeDRotation';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import CompareIcon from '@mui/icons-material/Compare';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

const GalleryContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  background: `linear-gradient(135deg, 
    ${theme.palette.background.default} 0%, 
    ${theme.palette.grey[50]} 100%)`,
  position: 'relative'
}));

const TreasureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.secondary.main}20`,
  transition: 'all 0.4s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-8px) rotateY(5deg)',
    boxShadow: theme.shadows[12],
    '& .treasure-image': {
      transform: 'scale(1.1) rotateZ(2deg)'
    }
  }
}));

const TreasureImage = styled(Box)(({ theme }) => ({
  width: '100%',
  height: 250,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  borderRadius: theme.shape.borderRadius,
  transition: 'all 0.4s ease',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(45deg, 
      ${theme.palette.primary.main}10 0%, 
      transparent 50%, 
      ${theme.palette.secondary.main}10 100%)`,
    opacity: 0,
    transition: 'opacity 0.3s ease'
  },
  '&:hover::before': {
    opacity: 1
  }
}));

const InteractionOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'rgba(0, 0, 0, 0.7)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 0,
  transition: 'opacity 0.3s ease',
  '&:hover': {
    opacity: 1
  }
}));

const FloatingButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: theme.palette.secondary.main,
  color: 'white',
  margin: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    transform: 'scale(1.1)'
  }
}));

interface Treasure {
  id: number;
  name: string;
  description: string;
  period: string;
  material: string;
  imageUrl: string;
  has3D: boolean;
  hasXRay: boolean;
  hasComparison: boolean;
}

const treasures: Treasure[] = [
  {
    id: 1,
    name: '真武铜像',
    description: '明代铸造的真武大帝铜像，工艺精湛，是武当山的镇山之宝。',
    period: '明代',
    material: '青铜',
    imageUrl: 'https://images.unsplash.com/photo-1577677202312-be699770fa4d?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxicm9uemUlMjBzdGF0dWUlMjBjaGluZXNlJTIwYXJ0aWZhY3QlMjByZWxpZ2lvdXN8ZW58MHwxfHx8MTc1NDYzNzg4MHww&ixlib=rb-4.1.0&q=85',
    has3D: true,
    hasXRay: true,
    hasComparison: true
  },
  {
    id: 2,
    name: '道教法器',
    description: '清代道教仪式用法器，包含铜钟、香炉等珍贵文物。',
    period: '清代',
    material: '黄铜',
    imageUrl: 'https://images.unsplash.com/photo-1583432311232-5c14e12983e2?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxicm9uemUlMjBzdGF0dWUlMjBjaGluZXNlJTIwYXJ0aWZhY3QlMjByZWxpZ2lvdXN8ZW58MHwxfHx8MTc1NDYzNzg4MHww&ixlib=rb-4.1.0&q=85',
    has3D: true,
    hasXRay: false,
    hasComparison: true
  },
  {
    id: 3,
    name: '古代经书',
    description: '宋代手抄道教经典，保存完好，具有极高的文献价值。',
    period: '宋代',
    material: '纸质',
    imageUrl: 'https://images.unsplash.com/photo-1621157107188-7d9d8edb3b83?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw0fHxicm9uemUlMjBzdGF0dWUlMjBjaGluZXNlJTIwYXJ0aWZhY3QlMjByZWxpZ2lvdXN8ZW58MHwxfHx8MTc1NDYzNzg4MHww&ixlib=rb-4.1.0&q=85',
    has3D: false,
    hasXRay: true,
    hasComparison: false
  }
];

const TreasuresGallery: React.FC = () => {
  const [selectedTreasure, setSelectedTreasure] = useState<number | null>(null);

  return (
    <GalleryContainer>
      <Stack spacing={6} alignItems="center">
        <Stack spacing={2} alignItems="center" textAlign="center">
          <Typography variant="h3" color="primary.main">
            武当文物珍宝馆
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600 }}>
            通过三维重建技术，近距离欣赏千年文物的精美细节
          </Typography>
        </Stack>

        <Box 
          sx={{ 
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' },
            gap: 4,
            width: '100%',
            maxWidth: 1200
          }}
        >
          {treasures.map((treasure) => (
            <TreasureCard 
              key={treasure.id}
              onClick={() => setSelectedTreasure(treasure.id)}
            >
              <Box sx={{ position: 'relative' }}>
                <TreasureImage
                  className="treasure-image"
                  sx={{
                    backgroundImage: `url(${treasure.imageUrl})`
                  }}
                />
                <InteractionOverlay>
                  <Stack direction="row" spacing={1}>
                    {treasure.has3D && (
                      <FloatingButton size="small">
                        <View3DIcon />
                      </FloatingButton>
                    )}
                    {treasure.hasXRay && (
                      <FloatingButton size="small">
                        <ZoomInIcon />
                      </FloatingButton>
                    )}
                    {treasure.hasComparison && (
                      <FloatingButton size="small">
                        <CompareIcon />
                      </FloatingButton>
                    )}
                  </Stack>
                </InteractionOverlay>
              </Box>
              
              <CardContent>
                <Stack spacing={2}>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    <Chip label={treasure.period} color="primary" size="small" />
                    <Chip label={treasure.material} color="secondary" size="small" />
                  </Stack>
                  
                  <Typography variant="h6" color="text.primary">
                    {treasure.name}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary">
                    {treasure.description}
                  </Typography>
                  
                  <Button
                    variant="outlined"
                    startIcon={<PlayArrowIcon />}
                    sx={{
                      borderColor: 'secondary.main',
                      color: 'secondary.main',
                      '&:hover': {
                        backgroundColor: 'secondary.main',
                        color: 'white'
                      }
                    }}
                  >
                    AR讲解
                  </Button>
                </Stack>
              </CardContent>
            </TreasureCard>
          ))}
        </Box>
        
        <Button
          variant="contained"
          size="large"
          sx={{
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            background: `linear-gradient(45deg, ${theme => theme.palette.primary.main}, ${theme => theme.palette.secondary.main})`,
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: 6
            }
          }}
        >
          查看更多文物
        </Button>
      </Stack>
    </GalleryContainer>
  );
};

export default TreasuresGallery;