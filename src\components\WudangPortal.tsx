import React from 'react';
import { Box, Container, AppBar, <PERSON><PERSON><PERSON>, <PERSON>po<PERSON>, Button, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import MenuIcon from '@mui/icons-material/Menu';
import HomeIcon from '@mui/icons-material/Home';
import InfoIcon from '@mui/icons-material/Info';
import MuseumIcon from '@mui/icons-material/Museum';
import SchoolIcon from '@mui/icons-material/School';

import HeroSection from './HeroSection';
import AnnouncementSection from './AnnouncementSection';
import TreasuresGallery from './TreasuresGallery';
import CulturalExperience from './CulturalExperience';
import HistoricalTimeline from './HistoricalTimeline';
import InstitutionalInfo from './InstitutionalInfo';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  boxShadow: 'none',
  borderBottom: `1px solid ${theme.palette.grey[200]}`
}));

const NavButton = styled(Button)(({ theme }) => ({
  color: theme.palette.text.primary,
  textTransform: 'none',
  fontSize: '1rem',
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: 'white'
  }
}));

const Footer = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  padding: theme.spacing(6, 0),
  marginTop: theme.spacing(8)
}));

const WudangPortal: React.FC = () => {
  return (
    <Box>
      {/* Navigation */}
      <StyledAppBar position="fixed">
        <Toolbar>
          <Typography 
            variant="h5" 
            component="div" 
            sx={{ 
              flexGrow: 1, 
              color: 'primary.main',
              fontFamily: '"Ma Shan Zheng", serif',
              fontWeight: 'bold'
            }}
          >
            数字武当
          </Typography>
          
          <Stack direction="row" spacing={2} sx={{ display: { xs: 'none', md: 'flex' } }}>
            <NavButton startIcon={<HomeIcon />}>
              首页
            </NavButton>
            <NavButton startIcon={<MuseumIcon />}>
              文物珍宝
            </NavButton>
            <NavButton startIcon={<SchoolIcon />}>
              文化体验
            </NavButton>
            <NavButton startIcon={<InfoIcon />}>
              关于我们
            </NavButton>
          </Stack>
          
          <MenuIcon sx={{ display: { xs: 'block', md: 'none' }, color: 'primary.main' }} />
        </Toolbar>
      </StyledAppBar>

      {/* Main Content */}
      <Box sx={{ pt: 8 }}>
        <HeroSection />
        <AnnouncementSection />
        <TreasuresGallery />
        <CulturalExperience />
        <HistoricalTimeline />
        <InstitutionalInfo />
      </Box>

      {/* Footer */}
      <Footer>
        <Container maxWidth="lg">
          <Stack spacing={4} alignItems="center" textAlign="center">
            <Typography variant="h4" sx={{ fontFamily: '"Ma Shan Zheng", serif' }}>
              数字武当
            </Typography>
            
            <Typography variant="h6">
              世界文化遗产数字保护工程
            </Typography>
            
            <Typography variant="body1" sx={{ maxWidth: 600 }}>
              传承千年道教文化，运用现代数字技术，
              为世界文化遗产的保护与传播贡献力量。
            </Typography>
            
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={4}>
              <Typography variant="body2">
                地址：湖北省十堰市武当山特区
              </Typography>
              <Typography variant="body2">
                电话：0719-5668567
              </Typography>
              <Typography variant="body2">
                邮箱：<EMAIL>
              </Typography>
            </Stack>
            
            <Typography variant="caption" color="rgba(255,255,255,0.7)">
              © 2024 武当山管理委员会 版权所有 | 鄂ICP备12345678号
            </Typography>
          </Stack>
        </Container>
      </Footer>
    </Box>
  );
};

export default WudangPortal;