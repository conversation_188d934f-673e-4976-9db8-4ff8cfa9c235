import React, { useState } from 'react';
import { Box, Typography, Stack, Card, CardContent, Chip } from '@mui/material';
import { styled } from '@mui/material/styles';
import LinearScaleIcon from '@mui/icons-material/LinearScale';

const TimelineContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  background: `linear-gradient(135deg, 
    ${theme.palette.primary.main}05 0%, 
    ${theme.palette.background.default} 50%,
    ${theme.palette.secondary.main}05 100%)`,
  position: 'relative'
}));

const TimelineTrack = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: 4,
  background: `linear-gradient(to bottom, 
    ${theme.palette.primary.main}, 
    ${theme.palette.secondary.main})`,
  margin: '0 auto',
  borderRadius: 2
}));

const TimelineNode = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: '50%',
  transform: 'translateX(-50%)',
  width: 20,
  height: 20,
  borderRadius: '50%',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  border: `3px solid ${theme.palette.background.paper}`,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  zIndex: 2,
  '&:hover': {
    transform: 'translateX(-50%) scale(1.5)',
    boxShadow: theme.shadows[8]
  }
}));

const TimelineCard = styled(Card)(({ theme }) => ({
  position: 'absolute',
  width: 300,
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.secondary.main}30`,
  transition: 'all 0.4s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: theme.shadows[12]
  }
}));

const DynastyIcon = styled(Box)(({ theme }) => ({
  width: 60,
  height: 60,
  borderRadius: '50%',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  fontSize: '1.5rem',
  fontWeight: 'bold',
  margin: '0 auto',
  marginBottom: theme.spacing(2)
}));

interface TimelineEvent {
  id: number;
  year: string;
  dynasty: string;
  title: string;
  description: string;
  significance: string;
  position: number; // 0-100 percentage position on timeline
  side: 'left' | 'right';
}

const timelineEvents: TimelineEvent[] = [
  {
    id: 1,
    year: '唐代',
    dynasty: '唐',
    title: '武当山道教兴起',
    description: '唐代时期，武当山开始成为道教修行的重要场所，众多道士在此修炼。',
    significance: '奠定了武当山道教文化的基础',
    position: 10,
    side: 'left'
  },
  {
    id: 2,
    year: '宋代',
    dynasty: '宋',
    title: '真武信仰确立',
    description: '宋代真武大帝信仰在武当山正式确立，成为道教重要神祇。',
    significance: '确立了武当山的宗教地位',
    position: 25,
    side: 'right'
  },
  {
    id: 3,
    year: '元代',
    dynasty: '元',
    title: '道教建筑扩建',
    description: '元代对武当山道教建筑进行大规模扩建，形成初具规模的建筑群。',
    significance: '为明代大规模建设奠定基础',
    position: 40,
    side: 'left'
  },
  {
    id: 4,
    year: '明代',
    dynasty: '明',
    title: '皇家道场建成',
    description: '明成祖朱棣下令大修武当山，建成规模宏大的皇家道场。',
    significance: '武当山达到历史巅峰',
    position: 60,
    side: 'right'
  },
  {
    id: 5,
    year: '清代',
    dynasty: '清',
    title: '文化传承延续',
    description: '清代继续维护武当山建筑，道教文化得以传承发展。',
    significance: '保持了文化的连续性',
    position: 75,
    side: 'left'
  },
  {
    id: 6,
    year: '现代',
    dynasty: '今',
    title: '数字化保护',
    description: '21世纪开始实施数字化保护工程，运用现代技术保护文化遗产。',
    significance: '传统文化与现代科技融合',
    position: 90,
    side: 'right'
  }
];

const HistoricalTimeline: React.FC = () => {
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);

  return (
    <TimelineContainer>
      <Stack spacing={6} alignItems="center">
        <Stack spacing={2} alignItems="center" textAlign="center">
          <Stack direction="row" alignItems="center" spacing={2}>
            <LinearScaleIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            <Typography variant="h3" color="primary.main">
              历史沿革时间轴
            </Typography>
          </Stack>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600 }}>
            穿越千年时光，见证武当山文化的传承与发展
          </Typography>
        </Stack>

        <Box sx={{ position: 'relative', width: '100%', maxWidth: 800, height: 800 }}>
          <TimelineTrack sx={{ height: '100%' }} />
          
          {timelineEvents.map((event) => (
            <React.Fragment key={event.id}>
              <TimelineNode
                sx={{ top: `${event.position}%` }}
                onClick={() => setSelectedEvent(selectedEvent === event.id ? null : event.id)}
              />
              
              <TimelineCard
                sx={{
                  top: `${event.position - 5}%`,
                  [event.side]: 40,
                  opacity: selectedEvent === event.id || selectedEvent === null ? 1 : 0.3,
                  transform: selectedEvent === event.id ? 'scale(1.05)' : 'scale(1)'
                }}
                onClick={() => setSelectedEvent(selectedEvent === event.id ? null : event.id)}
              >
                <CardContent>
                  <Stack spacing={2}>
                    <DynastyIcon>
                      {event.dynasty}
                    </DynastyIcon>
                    
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="h6" color="primary.main">
                        {event.title}
                      </Typography>
                      <Chip 
                        label={event.year} 
                        color="secondary" 
                        size="small" 
                      />
                    </Stack>
                    
                    <Typography variant="body2" color="text.secondary">
                      {event.description}
                    </Typography>
                    
                    {selectedEvent === event.id && (
                      <Box
                        sx={{
                          p: 2,
                          backgroundColor: 'grey.50',
                          borderRadius: 1,
                          animation: 'fadeIn 0.3s ease-in',
                          '@keyframes fadeIn': {
                            '0%': { opacity: 0, transform: 'translateY(10px)' },
                            '100%': { opacity: 1, transform: 'translateY(0)' }
                          }
                        }}
                      >
                        <Typography variant="caption" color="primary.main" fontWeight="bold">
                          历史意义：
                        </Typography>
                        <Typography variant="body2" color="text.primary">
                          {event.significance}
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </CardContent>
              </TimelineCard>
            </React.Fragment>
          ))}
        </Box>
        
        <Typography variant="body1" color="text.secondary" textAlign="center" sx={{ maxWidth: 600 }}>
          点击时间节点，探索武当山在不同历史时期的发展变迁，
          感受千年文化的深厚底蕴与传承脉络。
        </Typography>
      </Stack>
    </TimelineContainer>
  );
};

export default HistoricalTimeline;