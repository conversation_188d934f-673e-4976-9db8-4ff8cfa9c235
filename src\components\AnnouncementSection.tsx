import React, { useState } from 'react';
import { Box, Typography, Stack, Chip, IconButt<PERSON>, Card, CardContent } from '@mui/material';
import { styled } from '@mui/material/styles';
import NotificationsIcon from '@mui/icons-material/Notifications';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AnnouncementIcon from '@mui/icons-material/Announcement';

const ScrollContainer = styled(Box)(({ theme }) => ({
  background: `linear-gradient(90deg, 
    ${theme.palette.background.paper} 0%, 
    ${theme.palette.grey[50]} 50%, 
    ${theme.palette.background.paper} 100%)`,
  padding: theme.spacing(3, 0),
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: 'url("https://images.unsplash.com/photo-1523828792427-bdfc5ca6b91d?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw0fHxjaGluZXNlJTIwYXJjaGl0ZWN0dXJlJTIwd29vZGVuJTIwYnJhY2tldHMlMjB0cmFkaXRpb25hbCUyMHJvb2YlMjBkZXRhaWxzfGVufDB8MHx8fDE3NTQ2Mzc4ODB8MA&ixlib=rb-4.1.0&q=85")',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    opacity: 0.05,
    zIndex: 0
  }
}));

const ScrollingContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  animation: 'scroll 30s linear infinite',
  gap: theme.spacing(3),
  '@keyframes scroll': {
    '0%': {
      transform: 'translateX(100%)'
    },
    '100%': {
      transform: 'translateX(-100%)'
    }
  }
}));

const AnnouncementCard = styled(Card)(({ theme }) => ({
  minWidth: 300,
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.secondary.main}20`,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    background: 'rgba(255, 255, 255, 0.95)'
  }
}));

const BellIcon = styled(NotificationsIcon)(({ theme }) => ({
  color: theme.palette.secondary.main,
  animation: 'ring 2s ease-in-out infinite',
  '@keyframes ring': {
    '0%, 20%, 50%, 80%, 100%': {
      transform: 'rotate(0deg)'
    },
    '10%': {
      transform: 'rotate(10deg)'
    },
    '30%': {
      transform: 'rotate(-10deg)'
    },
    '40%': {
      transform: 'rotate(8deg)'
    },
    '60%': {
      transform: 'rotate(-8deg)'
    },
    '70%': {
      transform: 'rotate(6deg)'
    },
    '90%': {
      transform: 'rotate(-6deg)'
    }
  }
}));

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'urgent' | 'normal' | 'academic';
  date: string;
}

const announcements: Announcement[] = [
  {
    id: 1,
    title: '武当山金殿修缮工程启动',
    content: '为保护世界文化遗产，金殿将进行为期6个月的修缮工程，期间部分区域暂停开放。',
    type: 'urgent',
    date: '2024-01-15'
  },
  {
    id: 2,
    title: '数字化文物展览开放',
    content: '全新的3D文物展览馆正式开放，游客可通过VR技术近距离观赏珍贵文物。',
    type: 'normal',
    date: '2024-01-10'
  },
  {
    id: 3,
    title: '道教文化学术研讨会',
    content: '国际道教文化学术研讨会将于下月举行，邀请海内外专家学者共同探讨。',
    type: 'academic',
    date: '2024-01-08'
  },
  {
    id: 4,
    title: '春节期间开放时间调整',
    content: '春节期间，武当山景区开放时间调整为上午8:00至下午17:00。',
    type: 'normal',
    date: '2024-01-05'
  }
];

const AnnouncementSection: React.FC = () => {
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'urgent': return 'error';
      case 'academic': return 'primary';
      default: return 'secondary';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'urgent': return '紧急公告';
      case 'academic': return '学术活动';
      default: return '景区动态';
    }
  };

  return (
    <ScrollContainer>
      <Stack spacing={3} sx={{ position: 'relative', zIndex: 1 }}>
        <Stack direction="row" alignItems="center" justifyContent="center" spacing={2}>
          <BellIcon fontSize="large" />
          <Typography variant="h4" color="primary.main">
            公告资讯
          </Typography>
        </Stack>
        
        <Box sx={{ overflow: 'hidden', py: 2 }}>
          <ScrollingContent>
            {[...announcements, ...announcements].map((announcement, index) => (
              <AnnouncementCard 
                key={`${announcement.id}-${index}`}
                onClick={() => setExpandedCard(expandedCard === announcement.id ? null : announcement.id)}
              >
                <CardContent>
                  <Stack spacing={2}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Chip 
                        label={getTypeLabel(announcement.type)}
                        color={getTypeColor(announcement.type) as any}
                        size="small"
                        icon={announcement.type === 'urgent' ? <AnnouncementIcon /> : undefined}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {announcement.date}
                      </Typography>
                    </Stack>
                    
                    <Typography variant="h6" color="text.primary">
                      {announcement.title}
                    </Typography>
                    
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: expandedCard === announcement.id ? 'none' : 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}
                    >
                      {announcement.content}
                    </Typography>
                    
                    <IconButton 
                      size="small" 
                      sx={{ 
                        alignSelf: 'flex-end',
                        transform: expandedCard === announcement.id ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.3s ease'
                      }}
                    >
                      <ExpandMoreIcon />
                    </IconButton>
                  </Stack>
                </CardContent>
              </AnnouncementCard>
            ))}
          </ScrollingContent>
        </Box>
      </Stack>
    </ScrollContainer>
  );
};

export default AnnouncementSection;