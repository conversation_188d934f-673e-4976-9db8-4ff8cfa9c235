import React, { useState } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, Button, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import ArchitectureIcon from '@mui/icons-material/Architecture';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import VrIcon from '@mui/icons-material/Vrpano';

const ExperienceContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  background: `linear-gradient(180deg, 
    ${theme.palette.grey[50]} 0%, 
    ${theme.palette.background.default} 50%,
    ${theme.palette.grey[50]} 100%)`,
  position: 'relative'
}));

const ExperienceCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(15px)',
  border: `2px solid transparent`,
  borderImage: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main}) 1`,
  transition: 'all 0.4s ease',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(135deg, 
      ${theme.palette.primary.main}05 0%, 
      ${theme.palette.secondary.main}05 100%)`,
    opacity: 0,
    transition: 'opacity 0.3s ease'
  },
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: theme.shadows[16],
    '&::before': {
      opacity: 1
    }
  }
}));

const IconContainer = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  borderRadius: '50%',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto',
  marginBottom: theme.spacing(2),
  animation: 'float 3s ease-in-out infinite',
  '@keyframes float': {
    '0%, 100%': {
      transform: 'translateY(0px)'
    },
    '50%': {
      transform: 'translateY(-10px)'
    }
  }
}));

const TaiChiDemo = styled(Box)(({ theme }) => ({
  width: 120,
  height: 120,
  borderRadius: '50%',
  background: `conic-gradient(from 0deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main}, ${theme.palette.primary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto',
  marginBottom: theme.spacing(2),
  animation: 'rotate 8s linear infinite',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    width: '50%',
    height: '100%',
    background: theme.palette.background.paper,
    borderRadius: '60px 0 0 60px',
    left: '50%',
    transformOrigin: 'left center'
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    width: '50%',
    height: '100%',
    background: theme.palette.text.primary,
    borderRadius: '0 60px 60px 0',
    right: '50%',
    transformOrigin: 'right center'
  },
  '@keyframes rotate': {
    '0%': {
      transform: 'rotate(0deg)'
    },
    '100%': {
      transform: 'rotate(360deg)'
    }
  }
}));

interface Experience {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  type: 'taichi' | 'music' | 'architecture' | 'ritual' | 'seasonal';
  isInteractive: boolean;
}

const experiences: Experience[] = [
  {
    id: 1,
    title: '太极动画教学',
    description: '跟随3D动画学习正宗武当太极拳，感受道家养生文化的精髓。',
    icon: <PlayArrowIcon sx={{ color: 'white', fontSize: 40 }} />,
    type: 'taichi',
    isInteractive: true
  },
  {
    id: 2,
    title: '道教音乐体验',
    description: '聆听古韵悠长的道教音乐，体验音律与自然的和谐统一。',
    icon: <MusicNoteIcon sx={{ color: 'white', fontSize: 40 }} />,
    type: 'music',
    isInteractive: true
  },
  {
    id: 3,
    title: '建筑风水演示',
    description: '了解武当古建筑的风水布局，探索天人合一的建筑智慧。',
    icon: <ArchitectureIcon sx={{ color: 'white', fontSize: 40 }} />,
    type: 'architecture',
    isInteractive: false
  },
  {
    id: 4,
    title: '节气钟鼓互动',
    description: '点击虚拟编钟，聆听二十四节气的古老韵律。',
    icon: <AccessTimeIcon sx={{ color: 'white', fontSize: 40 }} />,
    type: 'seasonal',
    isInteractive: true
  },
  {
    id: 5,
    title: 'VR道教仪式',
    description: '沉浸式体验传统道教仪式，感受神圣庄严的宗教氛围。',
    icon: <VrIcon sx={{ color: 'white', fontSize: 40 }} />,
    type: 'ritual',
    isInteractive: true
  }
];

const CulturalExperience: React.FC = () => {
  const [activeExperience, setActiveExperience] = useState<number | null>(null);

  const renderSpecialIcon = (experience: Experience) => {
    if (experience.type === 'taichi') {
      return <TaiChiDemo />;
    }
    return (
      <IconContainer>
        {experience.icon}
      </IconContainer>
    );
  };

  return (
    <ExperienceContainer>
      <Stack spacing={6} alignItems="center">
        <Stack spacing={2} alignItems="center" textAlign="center">
          <Typography variant="h3" color="primary.main">
            武当文化体验区
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600 }}>
            沉浸式体验千年道教文化，感受传统与现代的完美融合
          </Typography>
        </Stack>

        <Box 
          sx={{ 
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' },
            gap: 4,
            width: '100%',
            maxWidth: 1200
          }}
        >
          {experiences.map((experience) => (
            <ExperienceCard 
              key={experience.id}
              onClick={() => setActiveExperience(experience.id)}
            >
              <CardContent sx={{ textAlign: 'center', p: 4 }}>
                <Stack spacing={3} alignItems="center">
                  {renderSpecialIcon(experience)}
                  
                  <Typography variant="h5" color="text.primary">
                    {experience.title}
                  </Typography>
                  
                  <Typography variant="body1" color="text.secondary">
                    {experience.description}
                  </Typography>
                  
                  {experience.isInteractive && (
                    <Button
                      variant="contained"
                      startIcon={<PlayArrowIcon />}
                      sx={{
                        background: `linear-gradient(45deg, ${theme => theme.palette.primary.main}, ${theme => theme.palette.secondary.main})`,
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 6
                        }
                      }}
                    >
                      开始体验
                    </Button>
                  )}
                </Stack>
              </CardContent>
            </ExperienceCard>
          ))}
        </Box>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>
          <Button
            variant="outlined"
            size="large"
            sx={{
              px: 4,
              py: 1.5,
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.main',
                color: 'white'
              }
            }}
          >
            预约VR体验
          </Button>
          
          <Button
            variant="outlined"
            size="large"
            sx={{
              px: 4,
              py: 1.5,
              borderColor: 'secondary.main',
              color: 'secondary.main',
              '&:hover': {
                backgroundColor: 'secondary.main',
                color: 'white'
              }
            }}
          >
            下载文化APP
          </Button>
        </Stack>
      </Stack>
    </ExperienceContainer>
  );
};

export default CulturalExperience;