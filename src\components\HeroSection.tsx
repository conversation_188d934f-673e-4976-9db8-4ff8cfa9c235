import React from 'react';
import { Box, Typography, Stack, Button, Container } from '@mui/material';
import { styled } from '@mui/material/styles';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

const HeroContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, 
    ${theme.palette.primary.main}20 0%, 
    ${theme.palette.secondary.main}10 50%, 
    ${theme.palette.background.default} 100%)`,
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: 'url("https://images.unsplash.com/photo-1530285897338-d9d80e81d078?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw4fHxtb3VudGFpbiUyMHRlbXBsZSUyMHRyYWRpdGlvbmFsJTIwYXJjaGl0ZWN0dXJlJTIwbWlzdCUyMHBlYWtzfGVufDB8MHx8fDE3NTQ2Mzc4ODB8MA&ixlib=rb-4.1.0&q=85")',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    opacity: 0.3,
    zIndex: -1
  }
}));

const CalligraphyTitle = styled(Typography)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
  animation: 'fadeInUp 2s ease-out',
  '@keyframes fadeInUp': {
    '0%': {
      opacity: 0,
      transform: 'translateY(30px)'
    },
    '100%': {
      opacity: 1,
      transform: 'translateY(0)'
    }
  }
}));

const FloatingNav = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  right: '2rem',
  transform: 'translateY(-50%)',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  zIndex: 10
}));

const NavDot = styled(Box)(({ theme }) => ({
  width: 12,
  height: 12,
  borderRadius: '50%',
  backgroundColor: theme.palette.secondary.main,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.5)',
    backgroundColor: theme.palette.primary.main
  }
}));

const HeroSection: React.FC = () => {
  return (
    <HeroContainer>
      <Container maxWidth="lg">
        <Stack
          spacing={4}
          alignItems="center"
          textAlign="center"
          sx={{ zIndex: 2, position: 'relative' }}
        >
          <CalligraphyTitle variant="h1">
            数字武当
          </CalligraphyTitle>
          
          <Typography 
            variant="h5" 
            color="text.secondary"
            sx={{ 
              maxWidth: '600px',
              animation: 'fadeInUp 2s ease-out 0.5s both'
            }}
          >
            世界文化遗产数字保护工程
          </Typography>
          
          <Typography 
            variant="body1" 
            color="text.secondary"
            sx={{ 
              maxWidth: '800px',
              fontSize: '1.1rem',
              animation: 'fadeInUp 2s ease-out 1s both'
            }}
          >
            探索千年道教圣地的数字化传承，体验三维重建的古建筑群，
            感受传统文化与现代科技的完美融合
          </Typography>
          
          <Stack 
            direction={{ xs: 'column', sm: 'row' }} 
            spacing={3}
            sx={{ 
              mt: 4,
              animation: 'fadeInUp 2s ease-out 1.5s both'
            }}
          >
            <Button
              variant="contained"
              size="large"
              startIcon={<PlayArrowIcon />}
              sx={{
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                background: `linear-gradient(45deg, ${theme => theme.palette.primary.main}, ${theme => theme.palette.secondary.main})`,
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 6
                }
              }}
            >
              开始探索
            </Button>
            
            <Button
              variant="outlined"
              size="large"
              sx={{
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                borderColor: 'secondary.main',
                color: 'secondary.main',
                '&:hover': {
                  backgroundColor: 'secondary.main',
                  color: 'white',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              了解更多
            </Button>
          </Stack>
        </Stack>
      </Container>
      
      <FloatingNav>
        <NavDot />
        <NavDot />
        <NavDot />
        <NavDot />
        <NavDot />
      </FloatingNav>
    </HeroContainer>
  );
};

export default HeroSection;