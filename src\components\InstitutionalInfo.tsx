import React, { useState } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Card, CardContent, Avatar, Chip, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import BusinessIcon from '@mui/icons-material/Business';
import PeopleIcon from '@mui/icons-material/People';
import HandshakeIcon from '@mui/icons-material/Handshake';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const InfoContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  background: `linear-gradient(180deg, 
    ${theme.palette.background.default} 0%, 
    ${theme.palette.grey[50]} 100%)`,
  position: 'relative'
}));

const InfoCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.secondary.main}20`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8]
  }
}));

const ExpertCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.primary.main}20`,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'scale(1.02)',
    boxShadow: theme.shadows[6],
    '& .expert-avatar': {
      transform: 'scale(1.1)'
    }
  }
}));

const PartnerLogo = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  borderRadius: '50%',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto',
  marginBottom: theme.spacing(1),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'rotateY(180deg)',
    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
  }
}));

interface Expert {
  id: number;
  name: string;
  title: string;
  specialization: string;
  achievements: string[];
  avatar: string;
}

interface Partner {
  id: number;
  name: string;
  type: string;
  description: string;
  projects: number;
}

const experts: Expert[] = [
  {
    id: 1,
    name: '李文华',
    title: '首席文物保护专家',
    specialization: '古建筑修复',
    achievements: ['国家级文物保护专家', '武当山修复工程总负责人', '发表学术论文50余篇'],
    avatar: 'https://i.pravatar.cc/150?img=1'
  },
  {
    id: 2,
    name: '王道明',
    title: '道教文化研究员',
    specialization: '道教历史文化',
    achievements: ['道教文化研究所所长', '《武当道教史》主编', '国际道教学会理事'],
    avatar: 'https://i.pravatar.cc/150?img=2'
  },
  {
    id: 3,
    name: '张数字',
    title: '数字化技术总监',
    specialization: '3D重建技术',
    achievements: ['数字文保技术专家', 'VR/AR应用开发', '获得技术专利15项'],
    avatar: 'https://i.pravatar.cc/150?img=3'
  }
];

const partners: Partner[] = [
  {
    id: 1,
    name: '清华大学',
    type: '学术合作',
    description: '建筑学院古建筑保护研究',
    projects: 8
  },
  {
    id: 2,
    name: '故宫博物院',
    type: '技术合作',
    description: '文物数字化保护技术',
    projects: 5
  },
  {
    id: 3,
    name: '腾讯科技',
    type: '技术支持',
    description: 'VR/AR技术开发',
    projects: 3
  },
  {
    id: 4,
    name: '联合国教科文组织',
    type: '国际合作',
    description: '世界文化遗产保护',
    projects: 2
  }
];

const InstitutionalInfo: React.FC = () => {
  const [selectedExpert, setSelectedExpert] = useState<number | null>(null);

  return (
    <InfoContainer>
      <Stack spacing={8}>
        {/* 机构设置 */}
        <Stack spacing={4} alignItems="center">
          <Stack direction="row" alignItems="center" spacing={2}>
            <BusinessIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            <Typography variant="h3" color="primary.main">
              机构设置
            </Typography>
          </Stack>
          
          <Box 
            sx={{ 
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' },
              gap: 4,
              width: '100%',
              maxWidth: 1000
            }}
          >
            <InfoCard>
              <CardContent sx={{ textAlign: 'center', p: 4 }}>
                <BusinessIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
                <Typography variant="h5" color="text.primary" gutterBottom>
                  管理委员会
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  负责武当山整体规划、保护管理和旅游开发的综合协调工作
                </Typography>
              </CardContent>
            </InfoCard>
            
            <InfoCard>
              <CardContent sx={{ textAlign: 'center', p: 4 }}>
                <TrendingUpIcon sx={{ fontSize: 60, color: 'secondary.main', mb: 2 }} />
                <Typography variant="h5" color="text.primary" gutterBottom>
                  文物保护部
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  专门负责文物古迹的保护、修复和数字化工程的实施
                </Typography>
              </CardContent>
            </InfoCard>
            
            <InfoCard>
              <CardContent sx={{ textAlign: 'center', p: 4 }}>
                <PeopleIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
                <Typography variant="h5" color="text.primary" gutterBottom>
                  研究院
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  从事道教文化研究、学术交流和文化传承工作
                </Typography>
              </CardContent>
            </InfoCard>
          </Box>
        </Stack>

        {/* 专家团队 */}
        <Stack spacing={4} alignItems="center">
          <Stack direction="row" alignItems="center" spacing={2}>
            <PeopleIcon sx={{ fontSize: 40, color: 'secondary.main' }} />
            <Typography variant="h3" color="secondary.main">
              专家团队
            </Typography>
          </Stack>
          
          <Box 
            sx={{ 
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' },
              gap: 4,
              width: '100%',
              maxWidth: 1000
            }}
          >
            {experts.map((expert) => (
              <ExpertCard 
                key={expert.id}
                onClick={() => setSelectedExpert(selectedExpert === expert.id ? null : expert.id)}
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar
                    src={expert.avatar}
                    alt={expert.name}
                    className="expert-avatar"
                    sx={{ 
                      width: 80, 
                      height: 80, 
                      mx: 'auto', 
                      mb: 2,
                      transition: 'transform 0.3s ease'
                    }}
                  />
                  
                  <Typography variant="h6" color="text.primary" gutterBottom>
                    {expert.name}
                  </Typography>
                  
                  <Typography variant="body2" color="primary.main" gutterBottom>
                    {expert.title}
                  </Typography>
                  
                  <Chip 
                    label={expert.specialization} 
                    color="secondary" 
                    size="small" 
                    sx={{ mb: 2 }}
                  />
                  
                  {selectedExpert === expert.id && (
                    <Box
                      sx={{
                        mt: 2,
                        p: 2,
                        backgroundColor: 'grey.50',
                        borderRadius: 1,
                        animation: 'fadeIn 0.3s ease-in'
                      }}
                    >
                      <Typography variant="caption" color="primary.main" fontWeight="bold">
                        主要成就：
                      </Typography>
                      {expert.achievements.map((achievement, index) => (
                        <Typography key={index} variant="caption" display="block" color="text.secondary">
                          • {achievement}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </CardContent>
              </ExpertCard>
            ))}
          </Box>
        </Stack>

        {/* 合作伙伴 */}
        <Stack spacing={4} alignItems="center">
          <Stack direction="row" alignItems="center" spacing={2}>
            <HandshakeIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            <Typography variant="h3" color="primary.main">
              合作伙伴
            </Typography>
          </Stack>
          
          <Box 
            sx={{ 
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' },
              gap: 4,
              width: '100%',
              maxWidth: 1000
            }}
          >
            {partners.map((partner) => (
              <InfoCard key={partner.id}>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <PartnerLogo>
                    <Typography variant="h6" color="primary.main">
                      {partner.name.charAt(0)}
                    </Typography>
                  </PartnerLogo>
                  
                  <Typography variant="h6" color="text.primary" gutterBottom>
                    {partner.name}
                  </Typography>
                  
                  <Chip 
                    label={partner.type} 
                    color="primary" 
                    size="small" 
                    sx={{ mb: 1 }}
                  />
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {partner.description}
                  </Typography>
                  
                  <Typography variant="caption" color="secondary.main">
                    合作项目：{partner.projects}个
                  </Typography>
                </CardContent>
              </InfoCard>
            ))}
          </Box>
          
          <Button
            variant="outlined"
            size="large"
            sx={{
              px: 4,
              py: 1.5,
              borderColor: 'secondary.main',
              color: 'secondary.main',
              '&:hover': {
                backgroundColor: 'secondary.main',
                color: 'white'
              }
            }}
          >
            查看更多合作机构
          </Button>
        </Stack>
      </Stack>
    </InfoContainer>
  );
};

export default InstitutionalInfo;